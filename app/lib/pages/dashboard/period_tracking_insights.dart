import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:auto_route/auto_route.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../custom_widgets/dotted_border_widget.dart';
import 'package:account_management/account_management.dart';
import '../../helpers.dart';

@RoutePage()
class PeriodTrackingInsightsPage extends StatelessWidget {
  const PeriodTrackingInsightsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<ManagePeriodTrackingBloc>(
            create: (context) => getIt<ManagePeriodTrackingBloc>()),
        BlocProvider<PeriodTrackingWatcherBloc>(
          create: (context) => getIt<PeriodTrackingWatcherBloc>()
            ..add(PeriodTrackingWatcherEvent.watchAllStarted()),
        ),
      ],
      child: PeriodTrackingInsightsScaffold(),
    );
  }
}

class PeriodTrackingInsightsScaffold extends StatefulWidget {
  const PeriodTrackingInsightsScaffold({super.key});

  @override
  State<PeriodTrackingInsightsScaffold> createState() =>
      _PeriodTrackingInsightsScaffoldState();
}

class _PeriodTrackingInsightsScaffoldState
    extends State<PeriodTrackingInsightsScaffold> {
  bool _isEditMode = false;
  bool _isDataPreloaded = false;

  // Pre-loaded data cache - all computed upfront
  Map<String, Map<String, dynamic>> _dateCache = {};
  String? _lastDataStateHash;
  DateTime? _todayNormalized;

  // Pre-computed lookup sets for O(1) date checking
  Set<String> _periodDateKeys = {};
  Set<String> _ovulationDateKeys = {};

  // Cycles cache
  List<List<DateTime>> _periodCyclesCache = [];
  List<List<DateTime>> _ovulationCyclesCache = [];

  // Pre-computed months list (cached to avoid recalculation)
  List<DateTime> _monthsToDisplay = [];

  // Generate list of months to display (past 1 year + current month + future 6 months)
  List<DateTime> _getMonthsToDisplay() {
    if (_monthsToDisplay.isNotEmpty) {
      return _monthsToDisplay; // Return cached list
    }

    final now = DateTime.now();
    List<DateTime> months = [];

    // Add past 12 months
    for (int i = 12; i >= 1; i--) {
      months.add(DateTime(now.year, now.month - i, 1));
    }

    // Add current month
    months.add(DateTime(now.year, now.month, 1));

    // Add future 6 months
    for (int i = 1; i <= 6; i++) {
      months.add(DateTime(now.year, now.month + i, 1));
    }

    _monthsToDisplay = months; // Cache the result
    return months;
  }

  // Clear cache only for specific date and surrounding dates that might be affected
  void _clearCacheForDate(DateTime date) {
    final dateKey = _getDateKey(date);

    // Remove from cache (will be recalculated)
    _dateCache.remove(dateKey);
    _lastDataStateHash = null; // Force recomputation

    // Also clear cache for surrounding dates that might be affected by period/ovulation cycles
    for (int i = -7; i <= 7; i++) {
      final surroundingDate = date.add(Duration(days: i));
      final surroundingKey = _getDateKey(surroundingDate);
      _dateCache.remove(surroundingKey);
    }
  }

  // Generate hash for data state to detect changes
  String _generateDataStateHash(
      Set<DateTime> periodDates, Set<DateTime> ovulationDates) {
    final periodHash =
        periodDates.map((d) => '${d.year}-${d.month}-${d.day}').join(',');
    final ovulationHash =
        ovulationDates.map((d) => '${d.year}-${d.month}-${d.day}').join(',');
    return '$periodHash|$ovulationHash';
  }

  // Pre-compute ALL date properties for ALL visible months upfront
  Future<void> _precomputeAllDateProperties(
      Set<DateTime> periodDates, Set<DateTime> ovulationDates) async {
    final currentHash = _generateDataStateHash(periodDates, ovulationDates);
    if (_lastDataStateHash == currentHash && _isDataPreloaded) {
      return; // Already computed for this data
    }

    _lastDataStateHash = currentHash;
    _dateCache.clear();
    _isDataPreloaded = false;

    // Update cycles cache first
    _updatePeriodCyclesCache(periodDates);
    _updateOvulationCyclesCache(ovulationDates);
    _updateLookupSets(periodDates, ovulationDates);

    // Pre-compute for ALL months upfront to eliminate rebuilds
    final months = _getMonthsToDisplay();

    // Process in batches to avoid blocking UI thread
    for (int i = 0; i < months.length; i++) {
      _precomputeMonthData(months[i], periodDates, ovulationDates);

      // Yield control back to UI thread every few months
      if (i % 3 == 0) {
        await Future<void>.delayed(Duration.zero);
      }
    }

    if (mounted) {
      setState(() {
        _isDataPreloaded = true;
      });
    }
  }

  // Pre-compute data for a specific month
  void _precomputeMonthData(
      DateTime month, Set<DateTime> periodDates, Set<DateTime> ovulationDates) {
    final daysInMonth = DateTime(month.year, month.month + 1, 0).day;
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(month.year, month.month, day);
      final dateKey = _getDateKey(date);

      _dateCache[dateKey] = {
        'isSelectedPeriodDate': _periodDateKeys.contains(dateKey),
        'isOvulationDate': _ovulationDateKeys.contains(dateKey),
        'isFirstPeriodDate': _isFirstPeriodDate(date, periodDates),
        'isLastPeriodDate': _isLastPeriodDate(date, periodDates),
        'isInPeriodWindow': _isInPeriodWindow(date, periodDates),
        'isFirstOvulationDate': _isFirstOvulationDate(date, ovulationDates),
        'isLastOvulationDate': _isLastOvulationDate(date, ovulationDates),
        'isInOvulationWindow': _isInOvulationWindow(date, ovulationDates),
      };
    }
  }

  // Generate date key for O(1) lookups
  String _getDateKey(DateTime date) {
    return '${date.year}-${date.month}-${date.day}';
  }

  // Update lookup sets for fast date checking
  void _updateLookupSets(
      Set<DateTime> periodDates, Set<DateTime> ovulationDates) {
    // Always update lookup sets (simplified approach)
    _periodDateKeys = periodDates.map(_getDateKey).toSet();
    _ovulationDateKeys = ovulationDates.map(_getDateKey).toSet();

    // Update today's date
    final today = DateTime.now();
    _todayNormalized = DateTime(today.year, today.month, today.day);
  }

  // Simplified method to update period cycles cache
  void _updatePeriodCyclesCache(Set<DateTime> periodDates) {
    _periodCyclesCache.clear();

    if (periodDates.isEmpty) return;

    List<DateTime> sortedPeriodDates = periodDates.toList()..sort();
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedPeriodDates.length; i++) {
      if (currentCycle.isEmpty) {
        currentCycle.add(sortedPeriodDates[i]);
      } else {
        DateTime lastDate = currentCycle.last;
        int daysDifference = sortedPeriodDates[i].difference(lastDate).inDays;

        if (daysDifference <= 1) {
          currentCycle.add(sortedPeriodDates[i]);
        } else {
          _periodCyclesCache.add(List.from(currentCycle));
          currentCycle = [sortedPeriodDates[i]];
        }
      }
    }
    if (currentCycle.isNotEmpty) {
      _periodCyclesCache.add(currentCycle);
    }
  }

  // Helper method to check if a date is in a period window (continuous flow between period dates)
  bool _isInPeriodWindow(DateTime date, Set<DateTime> periodDates) {
    final dateKey = _getDateKey(date);

    // Check cache first
    if (_dateCache.containsKey(dateKey) &&
        _dateCache[dateKey]!.containsKey('isInPeriodWindow')) {
      return _dateCache[dateKey]!['isInPeriodWindow'] as bool;
    }

    _updatePeriodCyclesCache(periodDates);
    bool result = false;

    // Check if date falls within any period cycle
    for (List<DateTime> cycle in _periodCyclesCache) {
      if (cycle.length >= 3) {
        DateTime startDate = cycle.first;
        DateTime endDate = cycle.last;

        if (date.isAfter(startDate) &&
            date.isBefore(endDate) &&
            !cycle.any((periodDate) =>
                periodDate.year == date.year &&
                periodDate.month == date.month &&
                periodDate.day == date.day)) {
          result = true;
          break;
        }
      }
    }

    // Cache result
    _dateCache[dateKey] = _dateCache[dateKey] ?? {};
    _dateCache[dateKey]!['isInPeriodWindow'] = result;
    return result;
  }

  // Helper method to check if a date is the first date in a period cycle
  bool _isFirstPeriodDate(DateTime date, Set<DateTime> periodDates) {
    final dateKey = _getDateKey(date);

    // Check cache first
    if (_dateCache.containsKey(dateKey) &&
        _dateCache[dateKey]!.containsKey('isFirstPeriodDate')) {
      return _dateCache[dateKey]!['isFirstPeriodDate'] as bool;
    }

    _updatePeriodCyclesCache(periodDates);
    bool result = false;

    // Check if date is the first date in any cycle
    for (List<DateTime> cycle in _periodCyclesCache) {
      DateTime firstDate = cycle.first;
      if (firstDate.year == date.year &&
          firstDate.month == date.month &&
          firstDate.day == date.day) {
        result = true;
        break;
      }
    }

    // Cache result
    _dateCache[dateKey] = _dateCache[dateKey] ?? {};
    _dateCache[dateKey]!['isFirstPeriodDate'] = result;
    return result;
  }

  // Helper method to check if a date is the last date in a period cycle
  bool _isLastPeriodDate(DateTime date, Set<DateTime> periodDates) {
    final dateKey = _getDateKey(date);

    // Check cache first
    if (_dateCache.containsKey(dateKey) &&
        _dateCache[dateKey]!.containsKey('isLastPeriodDate')) {
      return _dateCache[dateKey]!['isLastPeriodDate'] as bool;
    }

    _updatePeriodCyclesCache(periodDates);
    bool result = false;

    // Check if date is the last date in any cycle
    for (List<DateTime> cycle in _periodCyclesCache) {
      DateTime lastDate = cycle.last;
      if (lastDate.year == date.year &&
          lastDate.month == date.month &&
          lastDate.day == date.day) {
        result = true;
        break;
      }
    }

    // Cache result
    _dateCache[dateKey] = _dateCache[dateKey] ?? {};
    _dateCache[dateKey]!['isLastPeriodDate'] = result;
    return result;
  }

  // Simplified method to update ovulation cycles cache
  void _updateOvulationCyclesCache(Set<DateTime> ovulationDates) {
    _ovulationCyclesCache.clear();

    if (ovulationDates.isEmpty) return;

    List<DateTime> sortedOvulationDates = ovulationDates.toList()..sort();
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedOvulationDates.length; i++) {
      if (currentCycle.isEmpty) {
        currentCycle.add(sortedOvulationDates[i]);
      } else {
        DateTime lastDate = currentCycle.last;
        int daysDifference =
            sortedOvulationDates[i].difference(lastDate).inDays;

        if (daysDifference <= 1) {
          currentCycle.add(sortedOvulationDates[i]);
        } else {
          _ovulationCyclesCache.add(List.from(currentCycle));
          currentCycle = [sortedOvulationDates[i]];
        }
      }
    }
    if (currentCycle.isNotEmpty) {
      _ovulationCyclesCache.add(currentCycle);
    }
  }

  // Helper method to check if a date is in an ovulation window (fertile window around ovulation)
  bool _isInOvulationWindow(DateTime date, Set<DateTime> ovulationDates) {
    final dateKey = _getDateKey(date);

    // Check cache first
    if (_dateCache.containsKey(dateKey) &&
        _dateCache[dateKey]!.containsKey('isInOvulationWindow')) {
      return _dateCache[dateKey]!['isInOvulationWindow'] as bool;
    }

    bool result = false;
    for (DateTime ovulationDate in ovulationDates) {
      final difference = date.difference(ovulationDate).inDays.abs();
      if (difference <= 2 && difference > 0) {
        result = true;
        break;
      }
    }

    // Cache result
    _dateCache[dateKey] = _dateCache[dateKey] ?? {};
    _dateCache[dateKey]!['isInOvulationWindow'] = result;
    return result;
  }

  // Helper method to check if a date is the first date in an ovulation cycle
  bool _isFirstOvulationDate(DateTime date, Set<DateTime> ovulationDates) {
    final dateKey = _getDateKey(date);

    // Check cache first
    if (_dateCache.containsKey(dateKey) &&
        _dateCache[dateKey]!.containsKey('isFirstOvulationDate')) {
      return _dateCache[dateKey]!['isFirstOvulationDate'] as bool;
    }

    _updateOvulationCyclesCache(ovulationDates);
    bool result = false;

    // Check if date is the first date in any cycle
    for (List<DateTime> cycle in _ovulationCyclesCache) {
      DateTime firstDate = cycle.first;
      if (firstDate.year == date.year &&
          firstDate.month == date.month &&
          firstDate.day == date.day) {
        result = true;
        break;
      }
    }

    // Cache result
    _dateCache[dateKey] = _dateCache[dateKey] ?? {};
    _dateCache[dateKey]!['isFirstOvulationDate'] = result;
    return result;
  }

  // Helper method to check if a date is the last date in an ovulation cycle
  bool _isLastOvulationDate(DateTime date, Set<DateTime> ovulationDates) {
    final dateKey = _getDateKey(date);

    // Check cache first
    if (_dateCache.containsKey(dateKey) &&
        _dateCache[dateKey]!.containsKey('isLastOvulationDate')) {
      return _dateCache[dateKey]!['isLastOvulationDate'] as bool;
    }

    _updateOvulationCyclesCache(ovulationDates);
    bool result = false;

    // Check if date is the last date in any cycle
    for (List<DateTime> cycle in _ovulationCyclesCache) {
      DateTime lastDate = cycle.last;
      if (lastDate.year == date.year &&
          lastDate.month == date.month &&
          lastDate.day == date.day) {
        result = true;
        break;
      }
    }

    // Cache result
    _dateCache[dateKey] = _dateCache[dateKey] ?? {};
    _dateCache[dateKey]!['isLastOvulationDate'] = result;
    return result;
  }

  Widget _buildMonthCalendar(DateTime month, PeriodTrackingWatcherState state) {
    return RepaintBoundary(
      child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          child: Container(
              margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.2),
                    spreadRadius: 2,
                    blurRadius: 5,
                    offset: Offset(0, 3),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.only(
                    top: 16.h, left: 16.w, right: 16.w, bottom: 20.h),
                child: TableCalendar<DateTime>(
                  firstDay: DateTime.utc(2020, 1, 1),
                  lastDay: DateTime.utc(2030, 12, 31),
                  focusedDay: month,
                  calendarFormat: CalendarFormat.month,
                  startingDayOfWeek: StartingDayOfWeek.sunday,
                  availableGestures:
                      AvailableGestures.none, // Disable all swipe gestures
                  headerStyle: HeaderStyle(
                    formatButtonVisible: false,
                    titleCentered: true,
                    leftChevronVisible: false,
                    rightChevronVisible: false,
                    titleTextStyle: GoogleFonts.roboto(
                      fontSize: 30.sp,
                      fontWeight: FontWeight.w400,
                      color: Color(0xff6C618B),
                    ),
                  ),
                  daysOfWeekStyle: DaysOfWeekStyle(
                    weekdayStyle: GoogleFonts.roboto(
                      fontSize: 25.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(0xff71456F),
                    ),
                    weekendStyle: GoogleFonts.roboto(
                      fontSize: 25.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(0xff71456F),
                    ),
                  ),
                  onDaySelected: _isEditMode
                      ? (selectedDay, focusedDay) {
                          // Only allow editing past and current dates
                          final today = DateTime.now();
                          final selectedDateOnly = DateTime(selectedDay.year,
                              selectedDay.month, selectedDay.day);
                          final todayOnly =
                              DateTime(today.year, today.month, today.day);

                          if (selectedDateOnly.isAfter(todayOnly)) {
                            // Future date - show message and don't allow selection
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Cannot edit future dates'),
                                backgroundColor: Colors.orange,
                              ),
                            );
                            return;
                          }

                          // Only clear cache for affected dates, not entire cache
                          _clearCacheForDate(selectedDay);

                          // Toggle period date selection
                          context.read<ManagePeriodTrackingBloc>().add(
                              ManagePeriodTrackingEvent.selectDay(
                                  selectedDay, true));
                        }
                      : null,
                  calendarStyle: CalendarStyle(
                    outsideDaysVisible: false,
                    defaultTextStyle: GoogleFonts.roboto(
                      fontSize: 25.sp,
                      fontWeight: FontWeight.w400,
                      color: Color(0xff71456F),
                    ),
                    weekendTextStyle: GoogleFonts.roboto(
                      fontSize: 25.sp,
                      fontWeight: FontWeight.w400,
                      color: Color(0xff71456F),
                    ),
                  ),
                  calendarBuilders: CalendarBuilders(
                    defaultBuilder: (context, date, focusedDay) {
                      return RepaintBoundary(
                        child: _buildDayCell(date, state),
                      );
                    },
                    todayBuilder: (context, date, focusedDay) {
                      return RepaintBoundary(
                        child: _buildDayCell(date, state, isToday: true),
                      );
                    },
                    outsideBuilder: (context, date, focusedDay) {
                      return Container(); // Hide outside days
                    },
                  ),
                ),
              ))),
    );
  }

  Widget _buildDayCell(DateTime date, PeriodTrackingWatcherState state,
      {bool isToday = false}) {
    return state.maybeMap(
      data: (dataState) {
        final dateKey = _getDateKey(date);
        final dateNormalized = DateTime(date.year, date.month, date.day);

        // Get pre-loaded properties (should always be available now)
        final dateProperties = _dateCache[dateKey] ?? <String, dynamic>{};

        // Ultra-fast O(1) date property lookups from pre-loaded data
        bool isTodayDate = dateNormalized.isAtSameMomentAs(_todayNormalized!);
        bool isFutureDate = dateNormalized.isAfter(_todayNormalized!);
        bool isSelectedPeriodDate =
            (dateProperties['isSelectedPeriodDate'] as bool?) ?? false;
        bool isOvulationDate =
            (dateProperties['isOvulationDate'] as bool?) ?? false;
        bool isFirstPeriodDate =
            (dateProperties['isFirstPeriodDate'] as bool?) ?? false;
        bool isLastPeriodDate =
            (dateProperties['isLastPeriodDate'] as bool?) ?? false;
        bool isInPeriodWindow =
            (dateProperties['isInPeriodWindow'] as bool?) ?? false;
        bool isFirstOvulationDate =
            (dateProperties['isFirstOvulationDate'] as bool?) ?? false;
        bool isLastOvulationDate =
            (dateProperties['isLastOvulationDate'] as bool?) ?? false;
        bool isInOvulationWindow =
            (dateProperties['isInOvulationWindow'] as bool?) ?? false;

        // Build widget directly - all data is pre-loaded, no computation needed
        return _buildDayWidget(
          date: date,
          isTodayDate: isTodayDate,
          isFutureDate: isFutureDate,
          isSelectedPeriodDate: isSelectedPeriodDate,
          isFirstPeriodDate: isFirstPeriodDate,
          isLastPeriodDate: isLastPeriodDate,
          isInPeriodWindow: isInPeriodWindow,
          isOvulationDate: isOvulationDate,
          isFirstOvulationDate: isFirstOvulationDate,
          isLastOvulationDate: isLastOvulationDate,
          isInOvulationWindow: isInOvulationWindow,
        );
      },
      orElse: () => Container(
        width: 55.w,
        height: 55.h,
        child: Center(
          child: Text(
            '${date.day}',
            style: GoogleFonts.roboto(
              color: Color(0xff71456F),
              fontWeight: FontWeight.w400,
              fontSize: 25.sp,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      listener: (context, state) {
        state.maybeWhen(
          dataLoaded: () {
            // Period dates saved successfully
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Period dates updated successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            setState(() {
              _isEditMode = false;
            });
          },
          orElse: () {},
        );
      },
      child: Scaffold(
        backgroundColor: Color(0xffFAF2DF),
        appBar: AppBar(
          title: Text('Period Insights'),
          backgroundColor: Color(0xffFAF2DF),
          elevation: 0,
        ),
        body:
            BlocBuilder<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
          builder: (context, state) {
            return state.maybeMap(
              data: (dataState) {
                // Pre-load all data first (async)
                if (!_isDataPreloaded) {
                  _precomputeAllDateProperties(
                      dataState.selectedDays, dataState.ovulationDays);
                }

                // Show loading while pre-loading data
                if (!_isDataPreloaded) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          color: Color(0xff584294),
                        ),
                        SizedBox(height: 16.h),
                        Text(
                          'Loading calendar data...',
                          style: GoogleFonts.roboto(
                            color: Color(0xff71456F),
                            fontSize: 16.sp,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                final months = _getMonthsToDisplay();
                return Column(
                  children: [
                    Expanded(
                      child: ListView.builder(
                        physics: const BouncingScrollPhysics(),
                        itemCount: months.length,
                        itemBuilder: (context, index) {
                          return RepaintBoundary(
                            key: ValueKey(
                                'month_${months[index].year}_${months[index].month}'),
                            child: _buildMonthCalendar(months[index], state),
                          );
                        },
                        cacheExtent:
                            1000, // Increased since all data is pre-loaded
                        addAutomaticKeepAlives:
                            true, // Keep alive since data is pre-loaded
                        addRepaintBoundaries:
                            false, // We're adding them manually
                      ),
                    ),
                    // Edit button at the bottom
                    Padding(
                      padding: EdgeInsets.all(16.w),
                      child: GestureDetector(
                        onTap: () {
                          if (_isEditMode) {
                            // Save changes and exit edit mode
                            context.read<ManagePeriodTrackingBloc>().add(
                                  ManagePeriodTrackingEvent
                                      .savePeriodDatesAndRecalculate(),
                                );
                            setState(() {
                              _isEditMode = false;
                            });
                            // Don't clear cache when exiting edit mode - let it rebuild naturally
                          } else {
                            // Enter edit mode
                            setState(() {
                              _isEditMode = true;
                            });
                            // Don't clear cache when entering edit mode - let it rebuild naturally
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Tap on dates to select/deselect period days'),
                                backgroundColor: AppTheme.primaryColor,
                              ),
                            );
                          }
                        },
                        child: Container(
                          height: 45.h,
                          width: 100.w,
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            borderRadius: BorderRadius.circular(22.5),
                          ),
                          child: Center(
                            child: Text(
                              _isEditMode ? 'Done' : 'Edit',
                              style: GoogleFonts.roboto(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                                fontSize: 16.sp,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
              orElse: () => Center(child: CircularProgressIndicator()),
            );
          },
        ),
      ),
    );
  }

  // Main widget builder that uses modular helper methods
  Widget _buildDayWidget({
    required DateTime date,
    required bool isTodayDate,
    required bool isFutureDate,
    required bool isSelectedPeriodDate,
    required bool isFirstPeriodDate,
    required bool isLastPeriodDate,
    required bool isInPeriodWindow,
    required bool isOvulationDate,
    required bool isFirstOvulationDate,
    required bool isLastOvulationDate,
    required bool isInOvulationWindow,
  }) {
    // Priority order: Today > Period dates > Ovulation dates > Windows > Regular

    if (isTodayDate) {
      return _buildTodayDate(date);
    }

    // Period dates (first/last have priority over middle)
    if (isFirstPeriodDate) {
      return _buildPeriodFirstDate(date, isFutureDate);
    }
    if (isLastPeriodDate) {
      return _buildPeriodLastDate(date, isFutureDate);
    }
    if (isSelectedPeriodDate) {
      return _buildPeriodMiddleDate(date, isFutureDate);
    }

    // Ovulation dates (first/last have priority over middle)
    if (isFirstOvulationDate) {
      return _buildOvulationFirstDate(date, isFutureDate);
    }
    if (isLastOvulationDate) {
      return _buildOvulationLastDate(date, isFutureDate);
    }
    if (isOvulationDate) {
      return _buildOvulationMiddleDate(date, isFutureDate);
    }

    // Regular date
    return _buildRegularDate(date);
  }

  // Modular helper methods for better code organization

  Widget _buildTodayDate(DateTime date) {
    return Container(
      width: 55.w,
      height: 55.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Color(0xff5DADE2), width: 2),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff5DADE2),
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodFirstDate(DateTime date, bool isFuture) {
    return Container(
      width: 80.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            left: 12.0.w,
            top: 0,
            child: Container(
              width: 80.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(22.5.h),
                  bottomLeft: Radius.circular(22.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xff584294),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xff584294), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodLastDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the left
          Positioned(
            right: 15.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(27.5.h),
                  bottomRight: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xff584294),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xff584294), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodMiddleDate(DateTime date, bool isFuture) {
    return Container(
      width: 104.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: isFuture ? Color(0xff71456F) : Colors.white,
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildOvulationFirstDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            left: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(27.5.h),
                  bottomLeft: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: _buildRegularDate(date),
          ),
        ],
      ),
    );
  }

  Widget _buildOvulationLastDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Light purple extension to the right
          Positioned(
            right: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(27.5.h),
                  bottomRight: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: _buildRegularDate(date),
          ),
        ],
      ),
    );
  }

  Widget _buildOvulationMiddleDate(DateTime date, bool isFuture) {
    return Container(
      width: 104.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff71456F),
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildCircleContent(
      DateTime date, Color backgroundColor, Color textColor) {
    return Container(
      width: 60.w,
      height: 60.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor,
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: textColor,
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildRegularDate(DateTime date) {
    return RepaintBoundary(
      child: Container(
        width: 55.w,
        height: 55.h,
        child: Center(
          child: Text(
            '${date.day}',
            style: GoogleFonts.roboto(
              color: const Color(0xff71456F),
              fontWeight: FontWeight.w400,
              fontSize: 25.sp,
            ),
          ),
        ),
      ),
    );
  }
}
